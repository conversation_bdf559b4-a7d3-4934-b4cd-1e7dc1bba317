-- Complete fix for Clerk authentication error
-- This script updates the database to work with Clerk JWT tokens
-- 
-- IMPORTANT: This will clear existing data due to user_id type change
-- Run this in your Supabase SQL editor

-- Step 1: Disable <PERSON><PERSON> temporarily
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_identifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_diagnoses DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.community_posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.recovery_tracking DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop existing policies
DROP POLICY IF EXISTS "Users can view public profiles" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can delete their own profile" ON public.user_profiles;

-- Drop policies for other tables (you may need to adjust based on your existing policies)
DROP POLICY IF EXISTS "Users can view public identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can insert their own identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can update their own identifications" ON public.plant_identifications;
DROP POLICY IF EXISTS "Users can delete their own identifications" ON public.plant_identifications;

DROP POLICY IF EXISTS "Users can view public diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can insert their own diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can update their own diagnoses" ON public.plant_diagnoses;
DROP POLICY IF EXISTS "Users can delete their own diagnoses" ON public.plant_diagnoses;

-- Drop ALL recovery_tracking policies
DROP POLICY IF EXISTS "Users can view public recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can insert their own recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can update their own recovery tracking" ON public.recovery_tracking;
DROP POLICY IF EXISTS "Users can delete their own recovery tracking" ON public.recovery_tracking;

-- Drop community_posts policies if they exist
DROP POLICY IF EXISTS "Anyone can view community posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can insert their own posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON public.community_posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON public.community_posts;

-- Drop user_achievements policies if they exist
DROP POLICY IF EXISTS "Users can view their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can insert their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can update their own achievements" ON public.user_achievements;
DROP POLICY IF EXISTS "Users can delete their own achievements" ON public.user_achievements;

-- Step 3: Clear existing data (since we're changing user_id type)
TRUNCATE TABLE public.recovery_tracking RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.plant_diagnoses RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.plant_identifications RESTART IDENTITY CASCADE;
TRUNCATE TABLE public.user_profiles RESTART IDENTITY CASCADE;

-- Step 4: Change user_id columns from UUID to TEXT
ALTER TABLE public.user_profiles ALTER COLUMN user_id TYPE TEXT;
ALTER TABLE public.plant_identifications ALTER COLUMN user_id TYPE TEXT;
ALTER TABLE public.plant_diagnoses ALTER COLUMN user_id TYPE TEXT;
ALTER TABLE public.recovery_tracking ALTER COLUMN user_id TYPE TEXT;

-- Also update other tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'community_posts') THEN
        EXECUTE 'ALTER TABLE public.community_posts ALTER COLUMN user_id TYPE TEXT';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_achievements') THEN
        EXECUTE 'ALTER TABLE public.user_achievements ALTER COLUMN user_id TYPE TEXT';
    END IF;
END $$;

-- Step 5: Create new RLS policies using auth.jwt()->'user_metadata'->>'clerk_user_id'
-- User Profiles policies  
CREATE POLICY "Users can view public profiles" ON public.user_profiles
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id));

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
  FOR UPDATE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can delete their own profile" ON public.user_profiles
  FOR DELETE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

-- Plant Identifications policies
CREATE POLICY "Users can view public identifications" ON public.plant_identifications
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id));

CREATE POLICY "Users can insert their own identifications" ON public.plant_identifications
  FOR INSERT WITH CHECK (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can update their own identifications" ON public.plant_identifications
  FOR UPDATE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can delete their own identifications" ON public.plant_identifications
  FOR DELETE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

-- Plant Diagnoses policies
CREATE POLICY "Users can view public diagnoses" ON public.plant_diagnoses
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id));

CREATE POLICY "Users can insert their own diagnoses" ON public.plant_diagnoses
  FOR INSERT WITH CHECK (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can update their own diagnoses" ON public.plant_diagnoses
  FOR UPDATE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can delete their own diagnoses" ON public.plant_diagnoses
  FOR DELETE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

-- Recovery Tracking policies
CREATE POLICY "Users can view public recovery tracking" ON public.recovery_tracking
  FOR SELECT USING ((is_public = true) OR (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id));

CREATE POLICY "Users can insert their own recovery tracking" ON public.recovery_tracking
  FOR INSERT WITH CHECK (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can update their own recovery tracking" ON public.recovery_tracking
  FOR UPDATE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

CREATE POLICY "Users can delete their own recovery tracking" ON public.recovery_tracking
  FOR DELETE USING (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);

-- Step 6: Set defaults for user_profiles to enable automatic profile creation
ALTER TABLE public.user_profiles 
ALTER COLUMN display_name SET DEFAULT 'Plant Lover';

ALTER TABLE public.user_profiles 
ALTER COLUMN is_public SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN allow_garden_sharing SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN allow_profile_indexing SET DEFAULT false;

ALTER TABLE public.user_profiles 
ALTER COLUMN experience_level SET DEFAULT 'beginner';

ALTER TABLE public.user_profiles 
ALTER COLUMN total_identifications SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN total_diagnoses SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN community_points SET DEFAULT 0;

ALTER TABLE public.user_profiles 
ALTER COLUMN achievements SET DEFAULT '[]'::jsonb;

-- Step 7: Re-enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_identifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.plant_diagnoses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recovery_tracking ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'community_posts') THEN
        EXECUTE 'ALTER TABLE public.community_posts ENABLE ROW LEVEL SECURITY';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_achievements') THEN
        EXECUTE 'ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY';
    END IF;
END $$;

-- Add documentation comments
COMMENT ON TABLE public.user_profiles IS 'User profiles table with Clerk-compatible RLS policies using auth.jwt()->>''sub'' and TEXT user_id';
COMMENT ON TABLE public.plant_identifications IS 'Plant identifications with Clerk-compatible RLS policies using auth.jwt()->>''sub'' and TEXT user_id';
COMMENT ON TABLE public.plant_diagnoses IS 'Plant diagnoses with Clerk-compatible RLS policies using auth.jwt()->>''sub'' and TEXT user_id';
