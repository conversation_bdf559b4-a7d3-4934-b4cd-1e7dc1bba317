-- Configure Supa<PERSON> to validate Clerk JWT tokens
-- This script sets up <PERSON><PERSON><PERSON> to recognize and validate Clerk's JWT tokens
-- Run this in your Supabase SQL Editor

-- Step 1: Update Supabase Auth configuration to accept Clerk JWTs
-- You need to set the JWT Secret in Supabase Dashboard to match <PERSON>'s signing key

-- This is a helper function to test JWT token validation
-- Replace 'your_clerk_jwt_secret_here' with your actual Clerk JWT secret
CREATE OR REPLACE FUNCTION test_clerk_jwt() 
RETURNS text 
LANGUAGE plpgsql 
AS $$
BEGIN
  -- This function helps verify JWT configuration
  -- You can call it to test if JWT tokens are being recognized
  RETURN 'JWT configuration test function created';
END;
$$;

-- Create a test function that can be called to verify auth.jwt() is working
CREATE OR REPLACE FUNCTION get_current_jwt_claims()
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT auth.jwt();
$$;

-- Grant access to the test function
GRANT EXECUTE ON FUNCTION get_current_jwt_claims() TO public;

-- Create a debug function to help test user profile creation
CREATE OR REPLACE FUNCTION debug_user_profile_creation(test_user_id text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  jwt_claims jsonb;
  result jsonb;
BEGIN
  -- Get current JWT claims
  jwt_claims := auth.jwt();
  
  -- Return debug information
  result := jsonb_build_object(
    'jwt_claims', jwt_claims,
    'jwt_sub', jwt_claims->>'sub',
    'test_user_id', test_user_id,
    'sub_matches', (jwt_claims->>'sub' = test_user_id),
    'has_auth_role', jwt_claims->>'role' = 'authenticated'
  );
  
  RETURN result;
END;
$$;

-- Grant access to debug function
GRANT EXECUTE ON FUNCTION debug_user_profile_creation(text) TO public;

COMMENT ON FUNCTION test_clerk_jwt() IS 'Test function to verify Clerk JWT configuration';
COMMENT ON FUNCTION get_current_jwt_claims() IS 'Returns current JWT claims for debugging';
COMMENT ON FUNCTION debug_user_profile_creation(text) IS 'Debug function to help troubleshoot user profile creation';
