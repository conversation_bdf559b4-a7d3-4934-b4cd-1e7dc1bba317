-- <PERSON><PERSON>t to Truncate All Records from All Tables
-- WARNING: This will permanently delete all data from all tables
-- Use this script for fresh testing starts only
-- 
-- Execute these commands in the following order to handle foreign key constraints:

-- 1. Disable foreign key checks (if supported by your database)
-- Note: Supabase uses PostgreSQL, which doesn't have a global FK disable
-- Instead, we'll truncate in the correct order to respect dependencies

-- 2. Truncate tables in dependency order (child tables first, then parent tables)



-- Community posts may reference user_profiles and garden_collections
TRUNCATE TABLE community_posts RESTART IDENTITY CASCADE;

-- Garden collections depend on plant_identifications and user_profiles
TRUNCATE TABLE garden_collections RESTART IDENTITY CASCADE;

-- Plant diagnoses depend on plant_identifications and user_profiles
TRUNCATE TABLE plant_diagnoses RESTART IDENTITY CASCADE;

-- Plant identifications depend on user_profiles
TRUNCATE TABLE plant_identifications RESTART IDENTITY CASCADE;

-- User achievements depend on user_profiles
TRUNCATE TABLE user_achievements RESTART IDENTITY CASCADE;

-- User profiles (base table, no dependencies)
TRUNCATE TABLE user_profiles RESTART IDENTITY CASCADE;

-- 3. Reset sequences (RESTART IDENTITY CASCADE should handle this, but just in case)
-- Note: PostgreSQL auto-increment sequences should be reset by RESTART IDENTITY

-- 4. Verify all tables are empty (optional verification queries)
-- Uncomment these to verify the truncation was successful:

-- SELECT 'user_profiles' as table_name, COUNT(*) as record_count FROM user_profiles
-- UNION ALL
-- SELECT 'plant_identifications', COUNT(*) FROM plant_identifications
-- UNION ALL
-- SELECT 'plant_diagnoses', COUNT(*) FROM plant_diagnoses
-- UNION ALL
-- SELECT 'garden_collections', COUNT(*) FROM garden_collections
-- UNION ALL

-- UNION ALL
-- SELECT 'user_achievements', COUNT(*) FROM user_achievements
-- UNION ALL
-- SELECT 'community_posts', COUNT(*) FROM community_posts;

-- Alternative approach using a single command (if CASCADE works properly):
-- TRUNCATE TABLE 
--   user_profiles,
--   plant_identifications,
--   plant_diagnoses,
--   garden_collections,

--   user_achievements,
--   community_posts
-- RESTART IDENTITY CASCADE;

-- Notes:
-- - RESTART IDENTITY resets auto-increment sequences to start from 1
-- - CASCADE automatically truncates dependent tables
-- - This script assumes standard foreign key relationships
-- - Always backup your data before running this script
-- - Test in a development environment first
