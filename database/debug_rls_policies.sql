-- Debug script to check RLS policies and JWT structure
-- Run this in your Supabase SQL Editor while logged in to debug the JWT/RLS issue

-- Step 1: Check current RLS policies for user_profiles
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  cmd, 
  qual, 
  with_check 
FROM pg_policies 
WHERE tablename = 'user_profiles'
ORDER BY policyname;

-- Step 2: Test JWT claims (run this while logged in with <PERSON>)
SELECT 
  'Current JWT Claims' as test_name,
  auth.jwt() as full_jwt_claims;

-- Step 3: Test specific JWT claim extractions
SELECT 
  'JWT Claim Tests' as test_name,
  auth.jwt()->>'sub' as sub_claim,
  auth.jwt()->>'aud' as aud_claim,
  auth.jwt()->>'role' as role_claim,
  auth.jwt()->'user_metadata' as user_metadata_object,
  auth.jwt()->'user_metadata'->>'clerk_user_id' as clerk_user_id_claim;

-- Step 4: Test which RLS policy condition would work
SELECT 
  'RLS Policy Compatibility Test' as test_name,
  CASE 
    WHEN auth.jwt()->>'sub' IS NOT NULL THEN 'Use: auth.jwt()->>"sub"'
    WHEN auth.jwt()->'user_metadata'->>'clerk_user_id' IS NOT NULL THEN 'Use: auth.jwt()->''user_metadata''->>"clerk_user_id"'
    ELSE 'JWT claims not found - check JWT configuration'
  END as recommended_rls_policy;

-- Step 5: Test user profile access with current JWT
SELECT 
  'User Profile Access Test' as test_name,
  COUNT(*) as accessible_profiles
FROM user_profiles 
WHERE true; -- This will test if RLS allows any access

-- Step 6: Test specific user profile creation scenario
-- Replace 'your_clerk_user_id_here' with actual Clerk user ID from JWT
DO $$
DECLARE
  test_user_id text;
  jwt_sub text;
  jwt_clerk_id text;
BEGIN
  -- Get JWT claims
  jwt_sub := auth.jwt()->>'sub';
  jwt_clerk_id := auth.jwt()->'user_metadata'->>'clerk_user_id';
  
  -- Determine which user ID to use
  test_user_id := COALESCE(jwt_clerk_id, jwt_sub);
  
  RAISE NOTICE 'JWT sub claim: %', jwt_sub;
  RAISE NOTICE 'JWT clerk_user_id claim: %', jwt_clerk_id;
  RAISE NOTICE 'Test user_id to use: %', test_user_id;
  
  -- Test if we can insert a profile with current JWT
  IF test_user_id IS NOT NULL THEN
    BEGIN
      INSERT INTO user_profiles (user_id, display_name) 
      VALUES (test_user_id, 'Test Profile - DELETE ME')
      ON CONFLICT (user_id) DO NOTHING;
      
      RAISE NOTICE '✅ SUCCESS: Can insert user profile with user_id: %', test_user_id;
      
      -- Clean up test profile
      DELETE FROM user_profiles WHERE user_id = test_user_id AND display_name = 'Test Profile - DELETE ME';
      
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE '❌ FAILED: Cannot insert user profile. Error: %', SQLERRM;
    END;
  ELSE
    RAISE NOTICE '❌ FAILED: No valid user_id found in JWT claims';
  END IF;
END $$;

-- Step 7: Show current user_profiles table structure
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 8: Check if RLS is enabled
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'user_profiles';

-- Instructions for using this script:
-- 1. Login to your app with Clerk Google auth
-- 2. Copy and paste this entire script into Supabase SQL Editor
-- 3. Run the script
-- 4. Check the results to understand:
--    - What JWT claims are available
--    - Which RLS policy condition should be used
--    - Whether user profile creation would work
--    - What the specific error might be

-- Expected outcomes:
-- - If JWT claims show 'sub' with Clerk user ID: Use auth.jwt()->>'sub' in RLS policies
-- - If JWT claims show 'user_metadata.clerk_user_id': Use auth.jwt()->'user_metadata'->>'clerk_user_id' in RLS policies
-- - If test insert succeeds: RLS policies are correct
-- - If test insert fails: RLS policies need to be updated to match JWT structure
