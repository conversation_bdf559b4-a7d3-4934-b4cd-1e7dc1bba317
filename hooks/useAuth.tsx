import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-expo';
import { createClerkSupabaseClient } from '@/lib/supabase';
import { DatabaseService } from '@/services/database';

// Define a simplified user interface that matches what the app expects
interface AppUser {
  id: string;
  email?: string;
  user_metadata?: {
    full_name?: string;
    avatar_url?: string;
    preferred_username?: string;
  };
}

interface AuthContextType {
  user: AppUser | null;
  session: any | null; // Keep for compatibility but will be null
  loading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
  getSupabaseClient: () => any;
  getSupabaseClientWithValidation: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded } = useUser();
  const { signOut: clerkSignOut, getToken } = useClerkAuth();
  const [user, setUser] = useState<AppUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Helper function to ensure user profile exists with proper token validation
  const ensureUserProfileWithRetry = async (clerkUser: any, maxRetries = 3) => {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}: Checking for valid JWT token...`);

        // Wait for token to be available before making database calls
        const token = await getToken({ template: 'supabase' });

        if (token) {
          console.log('✅ Valid JWT token obtained, creating user profile...');
          const supabaseClient = await getSupabaseClientWithValidation();
          await DatabaseService.ensureUserProfileExists(clerkUser, supabaseClient);
          console.log('✅ User profile creation completed successfully');
          return; // Success - exit retry loop
        } else if (attempt < maxRetries) {
          console.log(`⏳ Attempt ${attempt}: No token yet, retrying in 500ms...`);
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          console.warn('❌ Final attempt: No valid JWT token available after all retries');
        }
      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          console.error('❌ All attempts failed to create user profile');
          // Don't throw error - let the user continue without profile for now
        } else if (attempt < maxRetries) {
          console.log(`⏳ Retrying in 500ms... (${maxRetries - attempt} attempts remaining)`);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
  };

  useEffect(() => {
    const handleUserChange = async () => {
      if (!isLoaded) {
        setLoading(true);
        return;
      }

      if (clerkUser) {
        // Convert Clerk user to our app user format
        const appUser: AppUser = {
          id: clerkUser.id,
          email: clerkUser.primaryEmailAddress?.emailAddress,
          user_metadata: {
            full_name: clerkUser.fullName || undefined,
            avatar_url: clerkUser.imageUrl || undefined,
            preferred_username: clerkUser.username || undefined,
          },
        };
        setUser(appUser);

        // Automatically ensure user profile exists using native integration
        // with proper token validation and retry logic
        try {
          await ensureUserProfileWithRetry(clerkUser);
        } catch (error) {
          console.error('Error ensuring user profile exists:', error);
          // Don't block user login if profile creation fails
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    };

    handleUserChange();
  }, [clerkUser, isLoaded]);

  const signOut = async () => {
    try {
      await clerkSignOut();
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    // With Clerk, tokens are automatically refreshed
    // Just return true if user is authenticated
    return !!clerkUser;
  };

  const isSessionValid = (): boolean => {
    return !!clerkUser;
  };

  const getSupabaseClient = () => {
    return createClerkSupabaseClient(async () => {
      try {
        if (!clerkUser) {
          console.warn('No Clerk user available for token generation');
          return null;
        }

        const token = await getToken({ template: 'supabase' });
        if (!token) {
          console.warn('Clerk token is null - check JWT template configuration');
        }
        return token;
      } catch (error) {
        console.error('Error getting Clerk token:', error);
        // Check if it's a template configuration error
        if (error instanceof Error && error.message.includes('template')) {
          console.error('JWT template "supabase" may not be configured in Clerk dashboard');
        }
        return null;
      }
    });
  };

  // New function that validates token availability before returning client
  const getSupabaseClientWithValidation = async () => {
    if (!clerkUser) {
      throw new Error('No Clerk user available for authenticated operations');
    }

    try {
      const token = await getToken({ template: 'supabase' });
      if (!token) {
        throw new Error('No valid JWT token available - check Clerk JWT template configuration');
      }

      console.log('✅ Valid JWT token obtained for Supabase client');
      return getSupabaseClient();
    } catch (error) {
      console.error('Error validating Clerk token for Supabase client:', error);
      throw error;
    }
  };

  const value = {
    user,
    session: null, // Keep for compatibility but always null with Clerk
    loading,
    signOut,
    refreshSession,
    isSessionValid,
    getSupabaseClient,
    getSupabaseClientWithValidation,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
