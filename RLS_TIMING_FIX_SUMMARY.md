# RLS Policy Timing Fix - Implementation Summary

## Problem Identified

The RLS policy violation error (`42501`) was occurring because:

1. **User signs in with Google OAuth** via Clerk
2. **Clerk session becomes active** (`clerkUser` becomes available)
3. **useAuth hook triggers** user profile creation immediately
4. **JWT token is not yet ready** when the database call is made
5. **Supabase receives upsert request** without valid JWT token
6. **RLS policy fails** because `auth.jwt()` returns `null`

## Root Cause

The timing issue was in the `useAuth` hook where `ensureUserProfileExists` was called immediately when `clerkUser` became available, but before the Clerk JWT token was fully ready for use.

## Solution Implemented

### 1. Enhanced Token Validation (`hooks/useAuth.tsx`)

#### Added `ensureUserProfileWithRetry` Function
- **Validates JWT token availability** before making database calls
- **Implements retry logic** with 500ms delays (max 3 attempts)
- **Comprehensive logging** for debugging token timing issues
- **Graceful error handling** that doesn't block user login

#### Added `getSupabaseClientWithValidation` Function
- **Validates token before creating client** 
- **Throws descriptive errors** if token is not available
- **Provides better debugging information**

### 2. Updated Database Operations (`hooks/useDatabase.ts`)

#### Replaced All Database Calls
- **Before**: Used `getSupabaseClient()` directly
- **After**: Uses `getSupabaseClientWithValidation()` 
- **Ensures**: JWT token is validated before every database operation

## Code Changes Made

### hooks/useAuth.tsx

```typescript
// NEW: Token validation with retry logic
const ensureUserProfileWithRetry = async (clerkUser: any, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const token = await getToken({ template: 'supabase' });
      if (token) {
        const supabaseClient = await getSupabaseClientWithValidation();
        await DatabaseService.ensureUserProfileExists(clerkUser, supabaseClient);
        return; // Success
      }
      // Retry logic with delays
    } catch (error) {
      // Error handling and retry logic
    }
  }
};

// NEW: Client with token validation
const getSupabaseClientWithValidation = async () => {
  if (!clerkUser) {
    throw new Error('No Clerk user available for authenticated operations');
  }
  
  const token = await getToken({ template: 'supabase' });
  if (!token) {
    throw new Error('No valid JWT token available');
  }
  
  return getSupabaseClient();
};
```

### hooks/useDatabase.ts

```typescript
// UPDATED: All methods now use validation
const getUserProfile = useCallback(async (userId: string) => {
  const client = await getSupabaseClientWithValidation(); // ✅ Token validated
  return DatabaseService.getUserProfile(userId, client);
}, [getSupabaseClientWithValidation]);
```

## Expected Behavior After Fix

### Successful Flow
1. **User signs in** with Google OAuth
2. **Clerk session becomes active**
3. **App waits for valid JWT token** (with retries if needed)
4. **Token validation succeeds**
5. **User profile creation proceeds** with authenticated client
6. **RLS policy passes** because JWT contains valid user ID
7. **User profile created successfully**

### Error Handling
- **Token not ready**: Retries up to 3 times with 500ms delays
- **Token never available**: Logs error but doesn't block login
- **Database errors**: Proper error logging and graceful degradation

## Testing the Fix

### Console Logs to Watch For
```
✅ Valid JWT token obtained, creating user profile...
✅ User profile creation completed successfully
```

### Error Logs (Should Not Appear)
```
❌ Error upserting user profile: {code: '42501', ...}
```

### Test Script
Run the test script to verify the fix:
```bash
node scripts/test-rls-fix.js
```

## Verification Steps

1. **Enable RLS policies** in Supabase (if disabled for testing)
2. **Test Google OAuth sign-in** in the app
3. **Check console logs** for token validation messages
4. **Verify user profiles** are created in Supabase dashboard
5. **Confirm no 42501 errors** in console

## Key Benefits

- ✅ **Eliminates RLS policy violations** during user sign-in
- ✅ **Maintains security** with RLS policies enabled
- ✅ **Provides better debugging** with comprehensive logging
- ✅ **Graceful error handling** that doesn't break user experience
- ✅ **Consistent token validation** across all database operations

## Files Modified

1. `hooks/useAuth.tsx` - Added token validation and retry logic
2. `hooks/useDatabase.ts` - Updated all methods to use validation
3. `scripts/test-rls-fix.js` - Test script for verification
4. `RLS_TIMING_FIX_SUMMARY.md` - This documentation

The fix ensures that all database operations wait for a valid Clerk JWT token before proceeding, eliminating the race condition that was causing RLS policy violations.
