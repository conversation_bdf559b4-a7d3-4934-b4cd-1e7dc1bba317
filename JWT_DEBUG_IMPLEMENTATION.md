# JWT Debug Implementation - Complete Solution

## Problem Summary
Users are getting RLS policy violation error when logging in with Clerk Google auth:
```
Error upserting user profile: {code: '42501', details: null, hint: null, message: 'new row violates row-level security policy for table "user_profiles"'}
```

## Root Cause Analysis
The issue is likely one of these:
1. **JWT Timing**: JWT session not fully established when user profile insert is attempted
2. **RLS Policy Mismatch**: RLS policies expect different JWT claim structure than what <PERSON> provides
3. **JWT Configuration**: Supabase not properly validating Clerk JWT tokens

## Implemented Debug Solution

### 1. Enhanced Database Service (`services/database.ts`)

#### Added `debugJWTSession()` Method
- **Logs complete JWT session state** before each database operation
- **Decodes JWT payload** to show actual claims structure
- **Tests RLS policy compatibility** by showing which claims are available
- **Tests database connection** with current JWT to identify auth issues

#### Enhanced `ensureUserProfileExists()` Method
- **Retry logic with 3 attempts** and exponential backoff
- **Comprehensive error logging** showing exact error codes and messages
- **JWT debugging before each attempt** to track session state
- **Proper error handling** that doesn't block user login

### 2. Enhanced Auth Hook (`hooks/useAuth.tsx`)

#### Updated `ensureUserProfileWithRetry()` Function
- **Increased retry delays** (1000ms instead of 500ms)
- **Added extra JWT processing delay** (500ms after token validation)
- **Exponential backoff** for retry attempts
- **Better logging** showing attempt numbers and timing

### 3. Debug Tools

#### SQL Debug Script (`database/debug_rls_policies.sql`)
Run this in Supabase SQL Editor while logged in to:
- **Check current RLS policies** and their conditions
- **Test JWT claims structure** to see what's actually in the token
- **Test RLS policy compatibility** to determine correct policy syntax
- **Simulate user profile creation** to identify exact failure point
- **Verify table structure** and RLS configuration

#### Client-Side Debug Script (`scripts/debug-clerk-jwt.js`)
Run this Node.js script to:
- **Test Supabase connection** without authentication
- **Analyze JWT token structure** when provided with real token
- **Test JWT authentication** with Supabase
- **Recommend correct RLS policy syntax** based on JWT structure

## How to Use This Debug Solution

### Step 1: Test the Enhanced Logging
1. **Login with Google auth** through Clerk
2. **Check browser console** for detailed JWT debug logs
3. **Look for these key indicators**:
   ```
   === JWT SESSION DEBUG START ===
   Session check: { hasSession: true/false, ... }
   JWT Payload: { sub: "...", user_metadata: {...}, ... }
   RLS Policy Tests: { "auth.jwt()->>'sub'": "...", ... }
   Database test with current JWT: { success: true/false, ... }
   ```

### Step 2: Run SQL Debug Script
1. **Copy `database/debug_rls_policies.sql`** content
2. **Paste into Supabase SQL Editor**
3. **Run while logged in** to your app
4. **Check results** to see:
   - Current RLS policies
   - Available JWT claims
   - Which RLS policy syntax to use
   - Whether test insert succeeds

### Step 3: Analyze Results

#### If JWT Session Debug Shows:
- **`hasSession: false`** → JWT timing issue, retry logic should help
- **`JWT Payload: null`** → JWT not being sent to Supabase properly
- **`Database test: { success: false }`** → RLS policy mismatch

#### If SQL Debug Shows:
- **`sub_claim: "user_2abc..."`** → Use `auth.jwt()->>'sub'` in RLS policies
- **`clerk_user_id_claim: "user_2abc..."`** → Use `auth.jwt()->'user_metadata'->>'clerk_user_id'` in RLS policies
- **Test insert fails** → RLS policies need updating

### Step 4: Fix Based on Results

#### For JWT Timing Issues:
The enhanced retry logic should resolve this automatically with:
- 3 retry attempts
- 1000ms delays between attempts
- Extra 500ms JWT processing delay

#### For RLS Policy Mismatch:
Update your RLS policies based on SQL debug results:

```sql
-- If JWT has user ID in 'sub' claim:
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.jwt()->>'sub' = user_id);

-- If JWT has user ID in 'user_metadata.clerk_user_id':
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.user_profiles;
CREATE POLICY "Users can insert their own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.jwt()->'user_metadata'->>'clerk_user_id' = user_id);
```

#### For JWT Configuration Issues:
1. **Check Clerk JWT template** has correct structure
2. **Verify Supabase JWT secret** matches Clerk signing key
3. **Ensure JWT template includes** required claims (`aud`, `role`, user ID)

## Expected Debug Output

### Successful Flow:
```
Profile creation attempt 1: Checking for valid JWT token...
✅ Valid JWT token obtained on attempt 1, creating user profile...
=== ATTEMPT 1/3 ===
=== JWT SESSION DEBUG START ===
Session check: { hasSession: true, sessionError: null, ... }
JWT Payload: { sub: "user_2abc...", aud: "authenticated", role: "authenticated", ... }
Database test with current JWT: { success: true, error: null }
=== JWT SESSION DEBUG END ===
Attempting upsert with profile data: { user_id: "user_2abc...", ... }
✅ Success on attempt 1
User profile upserted successfully: { user_id: "user_2abc...", ... }
```

### Failed Flow (with diagnosis):
```
Profile creation attempt 1: Checking for valid JWT token...
✅ Valid JWT token obtained on attempt 1, creating user profile...
=== ATTEMPT 1/3 ===
=== JWT SESSION DEBUG START ===
Session check: { hasSession: true, sessionError: null, ... }
JWT Payload: { sub: "user_2abc...", aud: "authenticated", role: "authenticated", ... }
Database test with current JWT: { success: false, error: "new row violates row-level security policy" }
=== JWT SESSION DEBUG END ===
Attempt 1 failed: { code: "42501", message: "new row violates row-level security policy for table \"user_profiles\"" }
```

## Next Steps After Implementation

1. **Test the login flow** and collect debug logs
2. **Run the SQL debug script** to identify JWT structure
3. **Update RLS policies** if needed based on JWT claims
4. **Verify JWT configuration** in Clerk and Supabase if issues persist

This comprehensive debug solution will definitively identify whether the issue is timing, RLS policy mismatch, or JWT configuration, and provide the exact information needed to fix it.
