# Complete Clerk + Supabase JWT Integration Guide

## The Problem
You're getting a `401 Unauthorized` error because Supabase doesn't recognize <PERSON>'s JWT tokens. The RLS policies are working, but they can't validate the JWT.

## Root Cause Analysis ✅ 
The diagnostic script confirms:
- ✅ Database connection works
- ✅ RLS policies are active and working
- ❌ JWT tokens are not being validated by Supabase
- ❌ `auth.jwt()->>'sub'` returns null instead of the Clerk user ID

## Solution: 3-Step JWT Configuration

### Step 1: Configure Clerk JWT Template

In your Clerk Dashboard:

1. Go to **JWT Templates**
2. Use the **existing "supabase" template** (default one)
3. Modify it to include the user ID in `user_metadata`:

```json
{
  "app_metadata": {},
  "aud": "authenticated",
  "email": "{{user.primary_email_address}}",
  "role": "authenticated",
  "user_metadata": {
    "clerk_user_id": "{{user.id}}"
  }
}
```

**Important**: 
- Keep `aud` as `"authenticated"`
- Keep `role` as `"authenticated"` 
- Add the Clerk user ID to `user_metadata.clerk_user_id`
- Don't modify the reserved claims (`iss`, `exp`, `iat`)

### Step 2: Get Clerk's JWT Signing Key

1. In Clerk Dashboard, go to **JWT Templates**
2. Click on your **"supabase"** template
3. Copy the **signing key** (it starts with something like `-----BEGIN PUBLIC KEY-----`)

### Step 3: Configure Supabase JWT Secret

1. Go to your **Supabase Dashboard**
2. Navigate to **Settings** → **API**
3. Scroll to **JWT Settings**
4. Replace the **JWT Secret** with Clerk's signing key from Step 2
5. Save the changes

## Verification Steps

### Test 1: Run the debug script
```bash
node scripts/debug-clerk-jwt.js
```

### Test 2: Test JWT validation in Supabase
1. Run the JWT configuration SQL:
```bash
# Copy database/configure_supabase_jwt_for_clerk.sql into Supabase SQL Editor and run it
```

### Test 3: Test with your app
1. Login with Google auth through Clerk
2. Check browser console - the 401 error should be gone
3. Verify user profile is created automatically

## Troubleshooting

### Issue: Still getting 401 errors
**Solution**: The JWT secret in Supabase doesn't match Clerk's signing key exactly.
- Double-check you copied the complete signing key
- Ensure no extra spaces or characters

### Issue: `auth.jwt()` returns null
**Solution**: JWT template configuration is incorrect.
- Verify the template has `aud: "authenticated"`
- Verify the template has `role: "authenticated"`
- Check that `sub` contains `{{user.id}}`

### Issue: User ID format mismatch
**Solution**: Ensure your database user_id columns are TEXT, not UUID.
- Run the database fix script if you haven't already

## Expected JWT Token Structure

When properly configured, your Clerk JWT should decode to:
```json
{
  "app_metadata": {},
  "aud": "authenticated",
  "email": "<EMAIL>",
  "role": "authenticated",
  "user_metadata": {
    "clerk_user_id": "user_2abcdef1234567890"
  }
}
```

The `user_metadata.clerk_user_id` field (`user_2abcdef1234567890`) should match the `user_id` in your database.

## Verification

After configuration, you should see:
1. No more 401 errors in console
2. User profiles created automatically on login
3. JWT claims properly recognized by RLS policies

The key insight: **Both sides need to match** - Clerk generates JWT tokens with specific claims, and Supabase needs the correct secret to validate them.
