#!/usr/bin/env node

/**
 * Debug script to test Clerk JW<PERSON> token generation and Supabase integration
 * Run this script to verify JWT tokens are being generated correctly
 * 
 * Usage: node scripts/debug-clerk-jwt.js
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration - update these with your actual values
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://zlivouxymzpbyoxnwxrp.supabase.co';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_KEY || 'sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC';

// Mock JWT token for testing (replace with actual token from your app)
const MOCK_JWT_TOKEN = 'your_actual_jwt_token_here';

function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    return { header, payload };
  } catch (error) {
    console.error('Error decoding JWT:', error.message);
    return null;
  }
}

async function testSupabaseConnection() {
  console.log('=== SUPABASE CONNECTION TEST ===');
  
  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('❌ Supabase connection failed:', error.message);
      return false;
    } else {
      console.log('✅ Supabase connection successful');
      return true;
    }
  } catch (error) {
    console.log('❌ Supabase connection error:', error.message);
    return false;
  }
}

async function testJWTWithSupabase(token) {
  console.log('=== JWT + SUPABASE TEST ===');
  
  if (!token || token === 'your_actual_jwt_token_here') {
    console.log('❌ No valid JWT token provided');
    console.log('To test with a real token:');
    console.log('1. Login to your app');
    console.log('2. Check browser console for JWT token');
    console.log('3. Replace MOCK_JWT_TOKEN in this script');
    return false;
  }

  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
    global: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  });

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('user_id')
      .limit(1);
    
    if (error) {
      console.log('❌ JWT authentication failed:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      return false;
    } else {
      console.log('✅ JWT authentication successful');
      console.log('Accessible profiles:', data?.length || 0);
      return true;
    }
  } catch (error) {
    console.log('❌ JWT test error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🔍 Clerk + Supabase JWT Debug Tool');
  console.log('=====================================\n');

  // Test 1: Basic Supabase connection
  const connectionOk = await testSupabaseConnection();
  console.log('');

  // Test 2: JWT token structure
  console.log('=== JWT TOKEN ANALYSIS ===');
  if (MOCK_JWT_TOKEN && MOCK_JWT_TOKEN !== 'your_actual_jwt_token_here') {
    const decoded = decodeJWT(MOCK_JWT_TOKEN);
    if (decoded) {
      console.log('JWT Header:', JSON.stringify(decoded.header, null, 2));
      console.log('JWT Payload:', JSON.stringify(decoded.payload, null, 2));
      
      // Check for required claims
      const { payload } = decoded;
      console.log('\n=== REQUIRED CLAIMS CHECK ===');
      console.log('✓ aud (audience):', payload.aud || '❌ Missing');
      console.log('✓ role:', payload.role || '❌ Missing');
      console.log('✓ sub (subject):', payload.sub || '❌ Missing');
      console.log('✓ user_metadata:', payload.user_metadata ? '✅ Present' : '❌ Missing');
      
      if (payload.user_metadata) {
        console.log('  - clerk_user_id:', payload.user_metadata.clerk_user_id || '❌ Missing');
      }
      
      // Recommend RLS policy
      console.log('\n=== RLS POLICY RECOMMENDATION ===');
      if (payload.sub) {
        console.log('✅ Use: auth.jwt()->>"sub" in RLS policies');
        console.log('   User ID value:', payload.sub);
      } else if (payload.user_metadata?.clerk_user_id) {
        console.log('✅ Use: auth.jwt()->\'user_metadata\'->>"clerk_user_id" in RLS policies');
        console.log('   User ID value:', payload.user_metadata.clerk_user_id);
      } else {
        console.log('❌ No suitable user ID claim found');
      }
    }
  } else {
    console.log('❌ No JWT token provided for analysis');
    console.log('To get a JWT token:');
    console.log('1. Login to your app with Clerk');
    console.log('2. Open browser developer tools');
    console.log('3. Look for JWT token in console logs');
    console.log('4. Update MOCK_JWT_TOKEN in this script');
  }
  console.log('');

  // Test 3: JWT with Supabase
  if (connectionOk) {
    await testJWTWithSupabase(MOCK_JWT_TOKEN);
  }

  console.log('\n=== NEXT STEPS ===');
  console.log('1. Run the SQL debug script in Supabase SQL Editor');
  console.log('2. Check your Clerk JWT template configuration');
  console.log('3. Verify Supabase JWT secret matches Clerk signing key');
  console.log('4. Update RLS policies to match JWT claim structure');
}

// Run the debug tool
main().catch(console.error);
